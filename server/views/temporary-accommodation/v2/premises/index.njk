{% from "govuk/components/button/macro.njk" import govukButton %}
{% from "govuk/components/table/macro.njk" import govukTable %}
{% from "moj/components/sub-navigation/macro.njk" import mojSubNavigation %}
{% from "../../../components/moj-cas-page-header-actions/macro.njk" import mojCasPageHeaderActions %}
{% from "govuk/components/breadcrumbs/macro.njk" import govukBreadcrumbs %}
{% from "govuk/components/input/macro.njk" import govukInput %}

{% extends "../../../partials/layout.njk" %}

{% set pageTitle = (isOnlineTab and "Online properties" or "Archived properties") + " - " + applicationName %}
{% set mainClasses = "app-container govuk-body" %}

{% block beforeContent %}
    {{ govukBreadcrumbs({
        items: [{
            text: "Home",
            href: "/"
        }]
    }) }}
{% endblock %}

{% block content %}
    <div>
        {% include "../../../_messages.njk" %}

        {{ mojCasPageHeaderActions({
            heading: {
                text: (isOnlineTab and "Online properties" or "Archived properties")
            },
            actions: [{
                text: "Add a property",
                href: "#"
            }]
        }) }}

        {{ mojSubNavigation({
            label: "Sub navigation",
            items: subNavArr
        }) }}
    </div>

    <div class="govuk-grid-row">
        {% if (totalPremises is defined and totalPremises > 0) or params.postcodeOrAddress %}
        <div class="govuk-grid-column-full">
            <div class="moj-search cas-search-container govuk-!-margin-bottom-6 govuk-!-padding-6 govuk-!-padding-bottom-7 govuk-grid-column-two-thirds">
                <form action="{{ (isOnlineTab and paths.premises.v2.online() or paths.premises.v2.archived()) }}" method="get">
                    {{ govukInput({
                    label: {
                    classes: 'govuk-!-font-weight-bold',
                    text: 'Find ' + (isOnlineTab and 'an online' or 'an archived') + ' property'
                    },
                    hint: {
                    text: 'Search by road or postcode'
                    },
                    name: 'postcodeOrAddress',
                    id: 'postcodeOrAddress',
                    value: params.postcodeOrAddress,
                    classes: 'moj-search__input'
                    }) }}

                    {{ govukButton({
                    text: 'Search',
                    attributes: { id: 'search-button' },
                    preventDoubleClick: true,
                    classes: 'moj-search__button'
                    }) }}
                </form>
            </div>
        </div>
        {% endif %}
        {% if totalPremises is defined %}
        <div class="govuk-grid-column-full">
            <div class="govuk-!-margin-bottom-6">
                <div class="govuk-!-padding-bottom-4">
                    <h2 class="govuk-heading-l govuk-!-margin-bottom-6">{{ totalPremises }} {{ (isOnlineTab and 'online' or 'archived') }} properties{% if params.postcodeOrAddress %} matching '{{ params.postcodeOrAddress }}'{% endif %}</h2>
                    {% if totalPremises is defined and totalPremises > 0 %}
                        {% if totalOnlineBedspaces is defined %}
                        <p class="govuk-body govuk-!-margin-bottom-1">Online bedspaces: {{ totalOnlineBedspaces }}</p>
                        {% endif %}
                        {% if totalUpcomingBedspaces is defined and totalUpcomingBedspaces != 0 %}
                        <p class="govuk-body govuk-!-margin-bottom-0">Upcoming bedspaces: {{ totalUpcomingBedspaces }}</p>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    <div>
        {% if (tableRows | length > 0) %}
            {{ govukTable({
                attributes: {
                    'data-module': 'moj-sortable-table'
                },
                captionClasses: "govuk-table__caption--m",
                head: [
                    {
                        text: "Address",
                        classes: "govuk-!-width-one-quarter"
                    },
                    {
                        text: "Bedspaces",
                        classes: "govuk-!-width-one-quarter"
                    },
                    {
                        text: "PDU",
                        attributes: { "aria-sort": "ascending" }
                    },
                    {
                        html: '<span class="govuk-visually-hidden">Actions</span>'
                    }
                ],
                rows: tableRows
            }) }}
        {% else %}
            {% if totalPremises is defined and totalPremises > 0 %}
                {% if params.postcodeOrAddress %}
                    <h2>{{ '0 ' + (isOnlineTab and 'online' or 'archived') + ' properties matching ‘' + params.postcodeOrAddress + '’' }}</h2>
                {% else %}
                    <h2>0 {{ (isOnlineTab and 'online' or 'archived') }} properties</h2>
                {% endif %}
            {% endif %}
        {% endif %}
    </div>

{% endblock %}
