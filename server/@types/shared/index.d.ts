/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type { ActiveOffence } from './models/ActiveOffence';
export type { Adjudication } from './models/Adjudication';
export type { AllocatedFilter } from './models/AllocatedFilter';
export type { ApArea } from './models/ApArea';
export type { Appeal } from './models/Appeal';
export type { AppealDecision } from './models/AppealDecision';
export type { Application } from './models/Application';
export type { ApplicationOrigin } from './models/ApplicationOrigin';
export type { ApplicationSortField } from './models/ApplicationSortField';
export type { ApplicationStatus } from './models/ApplicationStatus';
export type { ApplicationSummary } from './models/ApplicationSummary';
export type { ApplicationTimelineNote } from './models/ApplicationTimelineNote';
export type { ApprovedPremises } from './models/ApprovedPremises';
export type { ApprovedPremisesApplication } from './models/ApprovedPremisesApplication';
export type { ApprovedPremisesApplicationStatus } from './models/ApprovedPremisesApplicationStatus';
export type { ApprovedPremisesApplicationSummary } from './models/ApprovedPremisesApplicationSummary';
export type { ApprovedPremisesAssessment } from './models/ApprovedPremisesAssessment';
export type { ApprovedPremisesAssessmentStatus } from './models/ApprovedPremisesAssessmentStatus';
export type { ApprovedPremisesAssessmentSummary } from './models/ApprovedPremisesAssessmentSummary';
export type { ApprovedPremisesUser } from './models/ApprovedPremisesUser';
export type { ApprovedPremisesUserPermission } from './models/ApprovedPremisesUserPermission';
export type { ApprovedPremisesUserRole } from './models/ApprovedPremisesUserRole';
export type { ApType } from './models/ApType';
export type { Arrival } from './models/Arrival';
export type { Assessment } from './models/Assessment';
export type { AssessmentAcceptance } from './models/AssessmentAcceptance';
export type { AssessmentDecision } from './models/AssessmentDecision';
export type { AssessmentRejection } from './models/AssessmentRejection';
export type { AssessmentSortField } from './models/AssessmentSortField';
export type { AssessmentStatus } from './models/AssessmentStatus';
export type { AssessmentSummary } from './models/AssessmentSummary';
export type { AssessmentTask } from './models/AssessmentTask';
export type { Bed } from './models/Bed';
export type { BedSearchResultBedSummary } from './models/BedSearchResultBedSummary';
export type { BedSearchResultPremisesSummary } from './models/BedSearchResultPremisesSummary';
export type { BedSearchResultRoomSummary } from './models/BedSearchResultRoomSummary';
export type { BedspaceFilters } from './models/BedspaceFilters';
export type { BedStatus } from './models/BedStatus';
export type { BedSummary } from './models/BedSummary';
export type { Booking } from './models/Booking';
export type { BookingBody } from './models/BookingBody';
export type { BookingNotMade } from './models/BookingNotMade';
export type { BookingPremisesSummary } from './models/BookingPremisesSummary';
export type { BookingSearchResult } from './models/BookingSearchResult';
export type { BookingSearchResultBedSummary } from './models/BookingSearchResultBedSummary';
export type { BookingSearchResultBookingSummary } from './models/BookingSearchResultBookingSummary';
export type { BookingSearchResultPersonSummary } from './models/BookingSearchResultPersonSummary';
export type { BookingSearchResultPremisesSummary } from './models/BookingSearchResultPremisesSummary';
export type { BookingSearchResultRoomSummary } from './models/BookingSearchResultRoomSummary';
export type { BookingSearchResults } from './models/BookingSearchResults';
export type { BookingSearchSortField } from './models/BookingSearchSortField';
export type { BookingStatus } from './models/BookingStatus';
export type { CacheType } from './models/CacheType';
export type { Cancellation } from './models/Cancellation';
export type { CancellationReason } from './models/CancellationReason';
export type { Cas1ApplicationTimelinessCategory } from './models/Cas1ApplicationTimelinessCategory';
export type { Cas1ApplicationUserDetails } from './models/Cas1ApplicationUserDetails';
export type { Cas1ChangeRequestType } from './models/Cas1ChangeRequestType';
export type { Cas1CruManagementArea } from './models/Cas1CruManagementArea';
export type { Cas1KeyWorkerAllocation } from './models/Cas1KeyWorkerAllocation';
export type { Cas1SpaceBookingSummary } from './models/Cas1SpaceBookingSummary';
export type { Cas1SpaceBookingSummaryStatus } from './models/Cas1SpaceBookingSummaryStatus';
export type { Cas1SpaceCharacteristic } from './models/Cas1SpaceCharacteristic';
export type { Cas2TimelineEvent } from './models/Cas2TimelineEvent';
export type { Cas2User } from './models/Cas2User';
export type { Cas2v2Application } from './models/Cas2v2Application';
export type { Cas2v2Assessment } from './models/Cas2v2Assessment';
export type { Cas2v2StatusUpdate } from './models/Cas2v2StatusUpdate';
export type { Cas2v2StatusUpdateDetail } from './models/Cas2v2StatusUpdateDetail';
export type { Cas2v2User } from './models/Cas2v2User';
export type { Cas3Application } from './models/Cas3Application';
export type { Cas3ApplicationSummary } from './models/Cas3ApplicationSummary';
export type { Cas3Arrival } from './models/Cas3Arrival';
export type { Cas3Bedspace } from './models/Cas3Bedspace';
export type { Cas3BedspaceCharacteristic } from './models/Cas3BedspaceCharacteristic';
export type { Cas3BedspacePremisesSearchResult } from './models/Cas3BedspacePremisesSearchResult';
export type { Cas3Bedspaces } from './models/Cas3Bedspaces';
export type { Cas3BedspaceSearchParameters } from './models/Cas3BedspaceSearchParameters';
export type { Cas3BedspaceSearchResult } from './models/Cas3BedspaceSearchResult';
export type { Cas3BedspaceSearchResultOverlap } from './models/Cas3BedspaceSearchResultOverlap';
export type { Cas3BedspaceSearchResults } from './models/Cas3BedspaceSearchResults';
export type { Cas3BedspaceStatus } from './models/Cas3BedspaceStatus';
export type { Cas3Booking } from './models/Cas3Booking';
export type { Cas3BookingBody } from './models/Cas3BookingBody';
export type { Cas3BookingPremisesSummary } from './models/Cas3BookingPremisesSummary';
export type { Cas3BookingStatus } from './models/Cas3BookingStatus';
export type { Cas3Cancellation } from './models/Cas3Cancellation';
export type { Cas3Confirmation } from './models/Cas3Confirmation';
export type { Cas3Departure } from './models/Cas3Departure';
export type { Cas3Extension } from './models/Cas3Extension';
export type { Cas3NewApplication } from './models/Cas3NewApplication';
export type { Cas3NewBedspace } from './models/Cas3NewBedspace';
export type { Cas3NewDeparture } from './models/Cas3NewDeparture';
export type { Cas3NonArrival } from './models/Cas3NonArrival';
export type { Cas3OASysAssessmentMetadata } from './models/Cas3OASysAssessmentMetadata';
export type { Cas3OASysGroup } from './models/Cas3OASysGroup';
export type { Cas3Premises } from './models/Cas3Premises';
export type { Cas3PremisesSearchResult } from './models/Cas3PremisesSearchResult';
export type { Cas3PremisesSearchResults } from './models/Cas3PremisesSearchResults';
export type { Cas3PremisesSortBy } from './models/Cas3PremisesSortBy';
export type { Cas3PremisesStatus } from './models/Cas3PremisesStatus';
export type { Cas3PremisesSummary } from './models/Cas3PremisesSummary';
export type { Cas3ReportType } from './models/Cas3ReportType';
export type { Cas3SubmitApplication } from './models/Cas3SubmitApplication';
export type { Cas3Turnaround } from './models/Cas3Turnaround';
export type { Cas3UpdateApplication } from './models/Cas3UpdateApplication';
export type { Characteristic } from './models/Characteristic';
export type { CharacteristicPair } from './models/CharacteristicPair';
export type { ClarificationNote } from './models/ClarificationNote';
export type { Confirmation } from './models/Confirmation';
export type { DateCapacity } from './models/DateCapacity';
export type { DateChange } from './models/DateChange';
export type { DatePeriod } from './models/DatePeriod';
export type { Departure } from './models/Departure';
export type { DepartureReason } from './models/DepartureReason';
export type { DestinationProvider } from './models/DestinationProvider';
export type { Document } from './models/Document';
export type { DocumentLevel } from './models/DocumentLevel';
export type { Extension } from './models/Extension';
export type { ExternalUser } from './models/ExternalUser';
export type { FlagsEnvelope } from './models/FlagsEnvelope';
export type { FullPerson } from './models/FullPerson';
export type { FullPersonSummary } from './models/FullPersonSummary';
export type { FutureBooking } from './models/FutureBooking';
export type { Gender } from './models/Gender';
export type { InvalidParam } from './models/InvalidParam';
export type { LocalAuthorityArea } from './models/LocalAuthorityArea';
export type { LostBed } from './models/LostBed';
export type { LostBedCancellation } from './models/LostBedCancellation';
export type { LostBedReason } from './models/LostBedReason';
export type { LostBedStatus } from './models/LostBedStatus';
export type { Mappa } from './models/Mappa';
export type { MappaEnvelope } from './models/MappaEnvelope';
export type { MigrationJobRequest } from './models/MigrationJobRequest';
export type { MigrationJobType } from './models/MigrationJobType';
export type { MoveOnCategory } from './models/MoveOnCategory';
export type { NamedId } from './models/NamedId';
export type { NewAppeal } from './models/NewAppeal';
export type { NewApplication } from './models/NewApplication';
export type { NewApplicationTimelineNote } from './models/NewApplicationTimelineNote';
export type { NewArrival } from './models/NewArrival';
export type { NewBedMove } from './models/NewBedMove';
export type { NewBooking } from './models/NewBooking';
export type { NewBookingNotMade } from './models/NewBookingNotMade';
export type { NewCancellation } from './models/NewCancellation';
export type { NewCas2Arrival } from './models/NewCas2Arrival';
export type { NewCas3Arrival } from './models/NewCas3Arrival';
export type { NewClarificationNote } from './models/NewClarificationNote';
export type { NewConfirmation } from './models/NewConfirmation';
export type { NewDateChange } from './models/NewDateChange';
export type { NewDeparture } from './models/NewDeparture';
export type { NewExtension } from './models/NewExtension';
export type { NewLostBed } from './models/NewLostBed';
export type { NewLostBedCancellation } from './models/NewLostBedCancellation';
export type { NewPlacementApplication } from './models/NewPlacementApplication';
export type { NewPlacementRequestBooking } from './models/NewPlacementRequestBooking';
export type { NewPlacementRequestBookingConfirmation } from './models/NewPlacementRequestBookingConfirmation';
export type { NewPremises } from './models/NewPremises';
export type { NewReallocation } from './models/NewReallocation';
export type { NewReferralHistoryUserNote } from './models/NewReferralHistoryUserNote';
export type { NewRoom } from './models/NewRoom';
export type { NewTurnaround } from './models/NewTurnaround';
export type { NewWithdrawal } from './models/NewWithdrawal';
export type { NomisUser } from './models/NomisUser';
export type { Nonarrival } from './models/Nonarrival';
export type { NonArrivalReason } from './models/NonArrivalReason';
export type { OASysAssessmentId } from './models/OASysAssessmentId';
export type { OASysAssessmentState } from './models/OASysAssessmentState';
export type { OASysQuestion } from './models/OASysQuestion';
export type { OASysRiskOfSeriousHarm } from './models/OASysRiskOfSeriousHarm';
export type { OASysRiskToSelf } from './models/OASysRiskToSelf';
export type { OASysSection } from './models/OASysSection';
export type { OASysSections } from './models/OASysSections';
export type { OASysSupportingInformationQuestion } from './models/OASysSupportingInformationQuestion';
export type { OfflineApplication } from './models/OfflineApplication';
export type { OfflineApplicationSummary } from './models/OfflineApplicationSummary';
export type { Person } from './models/Person';
export type { PersonAcctAlert } from './models/PersonAcctAlert';
export type { PersonRisks } from './models/PersonRisks';
export type { PersonStatus } from './models/PersonStatus';
export type { PersonSummary } from './models/PersonSummary';
export type { PersonSummaryDiscriminator } from './models/PersonSummaryDiscriminator';
export type { PersonType } from './models/PersonType';
export type { PlacementApplication } from './models/PlacementApplication';
export type { PlacementApplicationDecision } from './models/PlacementApplicationDecision';
export type { PlacementApplicationDecisionEnvelope } from './models/PlacementApplicationDecisionEnvelope';
export type { PlacementApplicationTask } from './models/PlacementApplicationTask';
export type { PlacementApplicationType } from './models/PlacementApplicationType';
export type { PlacementCriteria } from './models/PlacementCriteria';
export type { PlacementDates } from './models/PlacementDates';
export type { PlacementRequest } from './models/PlacementRequest';
export type { PlacementRequestBookingSummary } from './models/PlacementRequestBookingSummary';
export type { PlacementRequestDetail } from './models/PlacementRequestDetail';
export type { PlacementRequestRequestType } from './models/PlacementRequestRequestType';
export type { PlacementRequestSortField } from './models/PlacementRequestSortField';
export type { PlacementRequestStatus } from './models/PlacementRequestStatus';
export type { PlacementRequirements } from './models/PlacementRequirements';
export type { PlacementType } from './models/PlacementType';
export type { Premises } from './models/Premises';
export type { PremisesBooking } from './models/PremisesBooking';
export type { PremisesFilters } from './models/PremisesFilters';
export type { PrisonCaseNote } from './models/PrisonCaseNote';
export type { ProbationDeliveryUnit } from './models/ProbationDeliveryUnit';
export type { ProbationRegion } from './models/ProbationRegion';
export type { Problem } from './models/Problem';
export type { ProfileResponse } from './models/ProfileResponse';
export type { PropertyStatus } from './models/PropertyStatus';
export type { Reallocation } from './models/Reallocation';
export type { ReferralHistoryDomainEventNote } from './models/ReferralHistoryDomainEventNote';
export type { ReferralHistoryNote } from './models/ReferralHistoryNote';
export type { ReferralHistoryNoteMessageDetails } from './models/ReferralHistoryNoteMessageDetails';
export type { ReferralHistorySystemNote } from './models/ReferralHistorySystemNote';
export type { ReferralHistoryUserNote } from './models/ReferralHistoryUserNote';
export type { ReferralRejectionReason } from './models/ReferralRejectionReason';
export type { ReleaseTypeOption } from './models/ReleaseTypeOption';
export type { RequestForPlacement } from './models/RequestForPlacement';
export type { RequestForPlacementStatus } from './models/RequestForPlacementStatus';
export type { RequestForPlacementType } from './models/RequestForPlacementType';
export type { RestrictedPerson } from './models/RestrictedPerson';
export type { RestrictedPersonSummary } from './models/RestrictedPersonSummary';
export type { RiskEnvelopeStatus } from './models/RiskEnvelopeStatus';
export type { RiskTier } from './models/RiskTier';
export type { RiskTierEnvelope } from './models/RiskTierEnvelope';
export type { RiskTierLevel } from './models/RiskTierLevel';
export type { Room } from './models/Room';
export type { RoshRisks } from './models/RoshRisks';
export type { RoshRisksEnvelope } from './models/RoshRisksEnvelope';
export type { SeedFileType } from './models/SeedFileType';
export type { SeedFromExcelDirectoryRequest } from './models/SeedFromExcelDirectoryRequest';
export type { SeedFromExcelFileRequest } from './models/SeedFromExcelFileRequest';
export type { SeedFromExcelFileType } from './models/SeedFromExcelFileType';
export type { SeedRequest } from './models/SeedRequest';
export type { SentenceTypeOption } from './models/SentenceTypeOption';
export type { ServiceName } from './models/ServiceName';
export type { SituationOption } from './models/SituationOption';
export type { SortDirection } from './models/SortDirection';
export type { SortOrder } from './models/SortOrder';
export type { StaffMember } from './models/StaffMember';
export type { SubmitApplication } from './models/SubmitApplication';
export type { SubmitApprovedPremisesApplication } from './models/SubmitApprovedPremisesApplication';
export type { SubmitPlacementApplication } from './models/SubmitPlacementApplication';
export type { SubmitTemporaryAccommodationApplication } from './models/SubmitTemporaryAccommodationApplication';
export type { Task } from './models/Task';
export type { TaskSortField } from './models/TaskSortField';
export type { TaskStatus } from './models/TaskStatus';
export type { TaskType } from './models/TaskType';
export type { TaskWrapper } from './models/TaskWrapper';
export type { Temporality } from './models/Temporality';
export type { TemporaryAccommodationApplication } from './models/TemporaryAccommodationApplication';
export type { TemporaryAccommodationApplicationSummary } from './models/TemporaryAccommodationApplicationSummary';
export type { TemporaryAccommodationAssessment } from './models/TemporaryAccommodationAssessment';
export type { TemporaryAccommodationAssessmentStatus } from './models/TemporaryAccommodationAssessmentStatus';
export type { TemporaryAccommodationAssessmentSummary } from './models/TemporaryAccommodationAssessmentSummary';
export type { TemporaryAccommodationPremises } from './models/TemporaryAccommodationPremises';
export type { TemporaryAccommodationUser } from './models/TemporaryAccommodationUser';
export type { TemporaryAccommodationUserRole } from './models/TemporaryAccommodationUserRole';
export type { TimelineEventType } from './models/TimelineEventType';
export type { Turnaround } from './models/Turnaround';
export type { Unit } from './models/Unit';
export type { UnknownPerson } from './models/UnknownPerson';
export type { UnknownPersonSummary } from './models/UnknownPersonSummary';
export type { UpdateApplication } from './models/UpdateApplication';
export type { UpdateApplicationType } from './models/UpdateApplicationType';
export type { UpdateApprovedPremisesApplication } from './models/UpdateApprovedPremisesApplication';
export type { UpdateAssessment } from './models/UpdateAssessment';
export type { UpdateCas2v2Application } from './models/UpdateCas2v2Application';
export type { UpdatedClarificationNote } from './models/UpdatedClarificationNote';
export type { UpdateLostBed } from './models/UpdateLostBed';
export type { UpdatePlacementApplication } from './models/UpdatePlacementApplication';
export type { UpdatePremises } from './models/UpdatePremises';
export type { UpdateRoom } from './models/UpdateRoom';
export type { UpdateTemporaryAccommodationApplication } from './models/UpdateTemporaryAccommodationApplication';
export type { User } from './models/User';
export type { UserQualification } from './models/UserQualification';
export type { UserRolesAndQualifications } from './models/UserRolesAndQualifications';
export type { UserSortField } from './models/UserSortField';
export type { UserSummary } from './models/UserSummary';
export type { UserWithWorkload } from './models/UserWithWorkload';
export type { ValidationError } from './models/ValidationError';
export type { Withdrawable } from './models/Withdrawable';
export type { Withdrawables } from './models/Withdrawables';
export type { WithdrawableType } from './models/WithdrawableType';
export type { WithdrawalReason } from './models/WithdrawalReason';
export type { WithdrawPlacementApplication } from './models/WithdrawPlacementApplication';
export type { WithdrawPlacementRequest } from './models/WithdrawPlacementRequest';
export type { WithdrawPlacementRequestReason } from './models/WithdrawPlacementRequestReason';
