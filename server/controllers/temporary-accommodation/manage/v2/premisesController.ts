import type { Request, RequestHandler, Response } from 'express'

import { PremisesSearchParameters } from '@approved-premises/ui'
import type { Cas3PremisesStatus } from '@approved-premises/api'
import PremisesService from '../../../../services/v2/premisesService'
import type { PremisesClientV2, RestClientBuilder } from '../../../../data'
import extractCallConfig from '../../../../utils/restUtils'

export default class PremisesController {
  constructor(
    private readonly premisesService: PremisesService,
    private readonly premisesClientFactory: RestClientBuilder<PremisesClientV2>,
  ) {}

  index(): RequestHandler {
    return async (req: Request, res: Response) => {
      const callConfig = extractCallConfig(req)

      const params = req.query as PremisesSearchParameters & { status?: Cas3PremisesStatus }

      // Default to 'online' if no status parameter provided
      let { status } = params
      if (!status) {
        status = 'online'
      }

      // Make API call directly in controller
      const premisesClient = this.premisesClientFactory(callConfig)
      const premises = await premisesClient.search(params.postcodeOrAddress ?? '', status)

      // Use service only for data transformation
      const tableRows = this.premisesService.tableRows(premises)

      return res.render('temporary-accommodation/v2/premises/index', {
        ...premises,
        tableRows,
        params: { ...params, status },
        status,
        isOnlineTab: status === 'online',
        isArchivedTab: status === 'archived',
      })
    }
  }
}
